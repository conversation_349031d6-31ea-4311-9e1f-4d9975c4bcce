# 优化周期功能说明

## 功能概述

根据量化对比实验结果，系统已优化为**10天优化周期**，这是经过实验验证的最优方案：

- **10天周期**：平均命中率87.95%，全中率66.82% ⭐ **最优**
- **20天周期**：平均命中率85.91%，全中率59.55%
- **30天周期**：平均命中率84.23%，全中率55.48%

## 新增功能

### 1. 自动优化提醒
- 程序启动时自动检查距离上次优化的时间
- 超过10天自动显示红色提醒条
- 显示具体的优化建议日期

### 2. 优化周期设置
- 新增"优化周期设置"按钮
- 可自定义优化周期（1-100天）
- 显示量化对比实验结果供参考

### 3. 智能状态显示
- **绿色状态**：距离下次优化还有X天
- **红色提醒**：需要优化，显示具体建议
- **欢迎信息**：首次使用时的引导

### 4. 自动记录优化日期
- 每次优化完成后自动记录日期
- 保存到`last_optimization.json`文件
- 下次启动时自动加载历史记录

## 使用方法

### 查看优化状态
程序启动后，底部会显示优化状态：
- 正常状态：绿色背景，显示剩余天数
- 需要优化：红色背景，显示提醒信息

### 设置优化周期
1. 点击"优化周期设置"按钮
2. 查看量化对比实验结果
3. 输入期望的优化周期（建议10天）
4. 点击"保存设置"

### 手动优化
1. 点击"自动优化参数"按钮
2. 设置优化参数（回测期数、目标命中率等）
3. 开始优化
4. 优化完成后自动记录日期

## 技术实现

### 文件结构
```
last_optimization.json  # 优化历史记录
{
  "last_date": "2025-07-23",  # 上次优化日期
  "cycle_days": 10            # 优化周期（天）
}
```

### 核心方法
- `load_last_optimization_date()`: 加载历史记录
- `save_last_optimization_date()`: 保存优化日期
- `check_optimization_reminder()`: 检查是否需要提醒
- `show_optimization_reminder()`: 显示优化提醒
- `show_optimization_cycle_dialog()`: 显示设置对话框

### 界面组件
- 优化提醒框架（底部黄色/红色条）
- 优化周期设置按钮
- 状态显示标签

## 最佳实践

### 推荐设置
- **优化周期**：10天（根据实验结果）
- **回测期数**：30-50期
- **目标命中率**：80%
- **最大迭代次数**：1000
- **种群大小**：50

### 使用建议
1. **定期优化**：每10天进行一次参数优化
2. **监控状态**：关注底部的优化状态显示
3. **数据更新**：有新数据时及时优化
4. **效果对比**：优化前后对比命中率变化

### 注意事项
- 优化过程可能需要较长时间，请耐心等待
- 建议在数据量充足时进行优化（至少100期）
- 优化完成后建议重新运行回测验证效果
- 可根据实际效果调整优化周期

## 更新日志

### v2.0 (当前版本)
- ✅ 新增10天优化周期功能
- ✅ 自动优化提醒系统
- ✅ 优化周期设置界面
- ✅ 智能状态显示
- ✅ 自动记录优化历史
- ✅ 量化对比实验验证

### 未来计划
- 🔄 优化算法进一步改进
- 🔄 更多周期选项测试
- 🔄 自动化优化调度
- 🔄 优化效果统计分析 