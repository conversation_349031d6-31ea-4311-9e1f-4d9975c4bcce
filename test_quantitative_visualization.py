#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import numpy as np

def test_quantitative_visualization():
    """测试量化对比可视化功能"""
    
    print("=== 量化对比可视化测试 ===\n")
    
    # 模拟量化对比数据
    quantitative_results = {
        10: {'avg_hit_rate': 0.8795, 'full_hit_rate': 0.6682},
        20: {'avg_hit_rate': 0.8591, 'full_hit_rate': 0.5955},
        30: {'avg_hit_rate': 0.8423, 'full_hit_rate': 0.5548}
    }
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(8, 10))
    fig.patch.set_facecolor('white')
    
    # 数据
    periods = [10, 20, 30]
    avg_hit_rates = [quantitative_results[p]['avg_hit_rate'] for p in periods]
    full_hit_rates = [quantitative_results[p]['full_hit_rate'] for p in periods]
    
    # 平均命中率图表
    bars1 = ax1.bar(periods, avg_hit_rates, color=['#4CAF50', '#FF9800', '#F44336'], alpha=0.8)
    ax1.set_title('平均命中率对比', fontsize=14, fontweight='bold', pad=15)
    ax1.set_ylabel('命中率', fontsize=12)
    ax1.set_ylim(0.8, 0.9)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, rate in zip(bars1, avg_hit_rates):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{rate*100:.2f}%', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    # 全中率图表
    bars2 = ax2.bar(periods, full_hit_rates, color=['#4CAF50', '#FF9800', '#F44336'], alpha=0.8)
    ax2.set_title('全中率对比', fontsize=14, fontweight='bold', pad=15)
    ax2.set_xlabel('优化周期（天）', fontsize=12)
    ax2.set_ylabel('全中率', fontsize=12)
    ax2.set_ylim(0.5, 0.7)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, rate in zip(bars2, full_hit_rates):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{rate*100:.2f}%', ha='center', va='bottom', fontweight='bold', fontsize=11)
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('quantitative_comparison_test.png', dpi=300, bbox_inches='tight')
    print("图表已保存为: quantitative_comparison_test.png")
    
    # 显示结果表格
    print("\n详细对比结果:")
    print("-" * 60)
    print(f"{'优化周期':<12} {'平均命中率':<12} {'全中率':<12} {'推荐指数':<12}")
    print("-" * 60)
    
    for period in [10, 20, 30]:
        avg_rate = quantitative_results[period]['avg_hit_rate']
        full_rate = quantitative_results[period]['full_hit_rate']
        
        if period == 10:
            recommendation = "⭐⭐⭐ 最优"
        elif period == 20:
            recommendation = "⭐⭐ 良好"
        else:
            recommendation = "⭐ 一般"
        
        print(f"{period}天{'':<8} {avg_rate*100:.2f}%{'':<6} {full_rate*100:.2f}%{'':<6} {recommendation}")
    
    print("-" * 60)
    print("\n结论：10天优化周期效果最佳，建议采用。")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    test_quantitative_visualization() 