import pandas as pd
import numpy as np
import json
from all import MFTNModel, DEFAULT_CONFIG, ParameterOptimizer

# 读取数据
df = pd.read_excel('data.xlsx')
assert all(col in df.columns for col in ['期号', '千位', '百位', '十位', '个位', '球五'])
data = df[['千位', '百位', '十位', '个位', '球五']].values

# 回测参数
period_options = [10, 20, 30]
window_size = 100  # 每次优化用的历史窗口长度（可调整）
version = 8  # 只测8位大奖

results = {}

for period in period_options:
    hit_rates = []
    full_hit_count = 0
    total_count = 0
    i = window_size
    while i + period <= len(data):
        train_data = data[i-window_size:i]
        test_data = data[i:i+period]
        # 用window_size历史优化参数
        def eval_params(params):
            model = MFTNModel(params, version)
            model.fit(train_data)
            hits = 0
            for j in range(len(test_data)):
                pred = model.predict_next()
                # 只看前4位
                if all(test_data[j][k] in pred[k] for k in range(4)):
                    hits += 1
            return hits / len(test_data)
        optimizer = ParameterOptimizer(target_hit_rate=0.8, max_iterations=20, population_size=20, num_threads=1)
        best_params, _ = optimizer.optimize(eval_params)
        # 用最优参数做回测
        model = MFTNModel(best_params, version)
        model.fit(train_data)
        for j in range(len(test_data)):
            pred = model.predict_next()
            pos_hits = [test_data[j][k] in pred[k] for k in range(4)]
            hit_rates.append(sum(pos_hits)/4)
            if all(pos_hits):
                full_hit_count += 1
            total_count += 1
        i += period
    avg_hit_rate = np.mean(hit_rates)
    full_hit_rate = full_hit_count / total_count if total_count else 0
    results[period] = {'avg_hit_rate': avg_hit_rate, 'full_hit_rate': full_hit_rate}
    print(f"周期{period}天：平均命中率{avg_hit_rate*100:.2f}%，全中率{full_hit_rate*100:.2f}%")

# 输出最优结果
best_period = max(results, key=lambda p: results[p]['full_hit_rate'])
best = results[best_period]
print(f"\n最优周期为{best_period}天，平均命中率{best['avg_hit_rate']*100:.2f}%，全中率{best['full_hit_rate']*100:.2f}%") 