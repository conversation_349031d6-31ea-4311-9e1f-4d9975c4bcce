#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import os
from datetime import datetime, timedelta

def test_optimization_cycle():
    """测试优化周期功能"""
    
    print("=== 优化周期功能测试 ===\n")
    
    # 测试场景1：首次使用（无历史记录）
    print("场景1：首次使用")
    if os.path.exists('last_optimization.json'):
        os.remove('last_optimization.json')
    
    # 模拟首次使用逻辑
    if not os.path.exists('last_optimization.json'):
        print("首次使用，显示欢迎信息：欢迎使用！建议每10天优化一次参数以获得最佳效果。")
    print()
    
    # 测试场景2：需要优化（超过10天）
    print("场景2：需要优化（超过10天）")
    last_date = datetime.now() - timedelta(days=12)  # 12天前
    last_date_str = last_date.strftime('%Y-%m-%d')
    
    test_data = {
        'last_date': last_date_str,
        'cycle_days': 10
    }
    
    with open('last_optimization.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    current_date = datetime.now()
    days_since_optimization = (current_date - last_date).days
    
    if days_since_optimization >= 10:
        next_optimization_date = last_date + timedelta(days=10)
        reminder_text = f"距离上次优化已过去{days_since_optimization}天，建议进行参数优化。\n" \
                      f"上次优化日期: {last_date_str}\n" \
                      f"建议下次优化日期: {next_optimization_date.strftime('%Y-%m-%d')}"
        print("需要优化提醒：")
        print(reminder_text)
    print()
    
    # 测试场景3：正常状态（未超过10天）
    print("场景3：正常状态（未超过10天）")
    last_date = datetime.now() - timedelta(days=5)  # 5天前
    last_date_str = last_date.strftime('%Y-%m-%d')
    
    test_data = {
        'last_date': last_date_str,
        'cycle_days': 10
    }
    
    with open('last_optimization.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    current_date = datetime.now()
    days_since_optimization = (current_date - last_date).days
    
    if days_since_optimization >= 10:
        next_optimization_date = last_date + timedelta(days=10)
        reminder_text = f"距离上次优化已过去{days_since_optimization}天，建议进行参数优化。\n" \
                      f"上次优化日期: {last_date_str}\n" \
                      f"建议下次优化日期: {next_optimization_date.strftime('%Y-%m-%d')}"
        print("需要优化提醒：")
        print(reminder_text)
    else:
        days_remaining = 10 - days_since_optimization
        next_optimization_date = last_date + timedelta(days=10)
        status_text = f"距离下次优化还有{days_remaining}天 (上次: {last_date_str}, 下次: {next_optimization_date.strftime('%Y-%m-%d')})"
        print("状态正常：")
        print(status_text)
    print()
    
    # 测试场景4：刚刚优化过
    print("场景4：刚刚优化过（今天）")
    last_date = datetime.now()  # 今天
    last_date_str = last_date.strftime('%Y-%m-%d')
    
    test_data = {
        'last_date': last_date_str,
        'cycle_days': 10
    }
    
    with open('last_optimization.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    current_date = datetime.now()
    days_since_optimization = (current_date - last_date).days
    
    if days_since_optimization >= 10:
        next_optimization_date = last_date + timedelta(days=10)
        reminder_text = f"距离上次优化已过去{days_since_optimization}天，建议进行参数优化。\n" \
                      f"上次优化日期: {last_date_str}\n" \
                      f"建议下次优化日期: {next_optimization_date.strftime('%Y-%m-%d')}"
        print("需要优化提醒：")
        print(reminder_text)
    else:
        days_remaining = 10 - days_since_optimization
        next_optimization_date = last_date + timedelta(days=10)
        status_text = f"距离下次优化还有{days_remaining}天 (上次: {last_date_str}, 下次: {next_optimization_date.strftime('%Y-%m-%d')})"
        print("状态正常：")
        print(status_text)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_optimization_cycle() 